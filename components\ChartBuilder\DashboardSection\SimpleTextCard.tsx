'use client'

/**
 * SimpleTextCard Component
 *
 * An enhanced text editor card for the dashboard that replaces the BlockNote editor.
 * This component allows users to add and edit text with formatting options in the dashboard.
 */

import { useState, useEffect, useRef } from 'react';
import { TextItem } from './types';
import {
  Grip, Trash2, Edit, Bold, Italic, Underline, AlignLeft,
  AlignCenter, AlignRight, AlignJustify, Type, Check, Square,
  Palette, ChevronDown, List, Heading1, Heading2, Heading3
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface SimpleTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
}

export function SimpleTextCard({ textItem, isEditMode, onUpdateText, onRemoveText }: SimpleTextCardProps) {
  // Theme support
  const { resolvedTheme } = useTheme();

  // State for UI interactions
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [editValue, setEditValue] = useState<string>(textItem.content || '');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State for formatting options
  const [textStyle, setTextStyle] = useState<string>(textItem.textStyle || 'normal');
  const [textAlign, setTextAlign] = useState<string>(textItem.textAlign || 'left');
  const [isBold, setIsBold] = useState<boolean>(textItem.isBold || false);
  const [isItalic, setIsItalic] = useState<boolean>(textItem.isItalic || false);
  const [isUnderline, setIsUnderline] = useState<boolean>(textItem.isUnderline || false);
  const [textColor, setTextColor] = useState<string>(textItem.color || '');
  const [bgColor, setBgColor] = useState<string>(textItem.backgroundColor || '');
  const [isChecklist, setIsChecklist] = useState<boolean>(textItem.isChecklist || false);
  const [checklistItems, setChecklistItems] = useState<Array<{text: string; checked: boolean}>>(
    textItem.checklistItems || []
  );

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize once on mount
  useEffect(() => {
    // If new item, enter edit mode after a brief delay
    if (textItem.isNew) {
      setTimeout(() => {
        setIsEditing(true);
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 100);
    }
  }, [textItem.isNew]);

  // Update edit value and formatting when content changes
  useEffect(() => {
    setEditValue(textItem.content || '');
    setTextStyle(textItem.textStyle || 'normal');
    setTextAlign(textItem.textAlign || 'left');
    setIsBold(textItem.isBold || false);
    setIsItalic(textItem.isItalic || false);
    setIsUnderline(textItem.isUnderline || false);
    setTextColor(textItem.color || '');
    setBgColor(textItem.backgroundColor || '');
    setIsChecklist(textItem.isChecklist || false);
    setChecklistItems(textItem.checklistItems || []);
  }, [textItem]);

  // Handle double click to edit
  const handleDoubleClick = () => {
    if (isEditMode && !isEditing) {
      setIsEditing(true);
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 10);
    }
  };

  // State for text selection
  const [selectionStart, setSelectionStart] = useState<number>(0);
  const [selectionEnd, setSelectionEnd] = useState<number>(0);
  const [selectedText, setSelectedText] = useState<string>('');

  // Handle text selection
  const handleTextSelection = () => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      setSelectionStart(start);
      setSelectionEnd(end);
      setSelectedText(editValue.substring(start, end));
    }
  };

  // Apply formatting to selected text only
  const applyFormatting = (formatType: string, value: any) => {
    if (textareaRef.current) {
      const start = selectionStart;
      const end = selectionEnd;

      // If no text is selected, apply to the whole text (for alignment and background color)
      if (start === end && (formatType === 'align' || formatType === 'bgColor')) {
        switch (formatType) {
          case 'align':
            setTextAlign(value);
            break;
          case 'bgColor':
            setBgColor(value);
            break;
        }
        return;
      }

      // If no text is selected for other formatting, do nothing
      if (start === end) return;

      // Get the text before and after the selection
      const beforeText = editValue.substring(0, start);
      const selectedText = editValue.substring(start, end);
      const afterText = editValue.substring(end);

      // Apply formatting based on type
      let formattedText = selectedText;

      switch (formatType) {
        case 'bold':
          setIsBold(!isBold);
          formattedText = isBold ? selectedText : `**${selectedText}**`;
          break;
        case 'italic':
          setIsItalic(!isItalic);
          formattedText = isItalic ? selectedText : `*${selectedText}*`;
          break;
        case 'underline':
          setIsUnderline(!isUnderline);
          formattedText = isUnderline ? selectedText : `__${selectedText}__`;
          break;
        case 'heading':
          switch (value) {
            case 'h1':
              formattedText = `# ${selectedText}`;
              break;
            case 'h2':
              formattedText = `## ${selectedText}`;
              break;
            case 'h3':
              formattedText = `### ${selectedText}`;
              break;
            case 'h4':
              formattedText = `#### ${selectedText}`;
              break;
            default:
              formattedText = selectedText;
          }
          setTextStyle(value);
          break;
        case 'color':
          setTextColor(value);
          formattedText = value ? `<span style="color:${value}">${selectedText}</span>` : selectedText;
          break;
      }

      // Update the text with the formatted selection
      const newText = beforeText + formattedText + afterText;
      setEditValue(newText);

      // Update the selection range to include the formatting
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(start, start + formattedText.length);
          handleTextSelection();
        }
      }, 0);
    }
  };

  // Handle formatting changes
  const toggleBold = () => applyFormatting('bold', null);
  const toggleItalic = () => applyFormatting('italic', null);
  const toggleUnderline = () => applyFormatting('underline', null);

  const toggleChecklist = () => {
    if (!isChecklist && editValue) {
      // Convert current text to checklist items
      const lines = editValue.split('\n').filter(line => line.trim() !== '');
      setChecklistItems(lines.map(line => ({ text: line, checked: false })));
      setEditValue(''); // Clear the textarea
    } else if (isChecklist && checklistItems.length > 0) {
      // Convert checklist back to text
      const text = checklistItems.map(item => item.text).join('\n');
      setEditValue(text);
      setChecklistItems([]);
    }
    setIsChecklist(!isChecklist);
  };

  const setHeadingStyle = (style: string) => {
    applyFormatting('heading', style);
  };

  const setAlignment = (align: 'left' | 'center' | 'right' | 'justify') => {
    applyFormatting('align', align);
  };

  const handleColorChange = (color: string) => {
    applyFormatting('color', color);
  };

  const handleBgColorChange = (color: string) => {
    applyFormatting('bgColor', color);
  };

  const toggleChecklistItem = (index: number) => {
    const newItems = [...checklistItems];
    newItems[index].checked = !newItems[index].checked;
    setChecklistItems(newItems);
  };

  const updateChecklistItemText = (index: number, text: string) => {
    const newItems = [...checklistItems];
    newItems[index].text = text;
    setChecklistItems(newItems);
  };

  const addChecklistItem = () => {
    setChecklistItems([...checklistItems, { text: '', checked: false }]);
  };

  const removeChecklistItem = (index: number) => {
    const newItems = [...checklistItems];
    newItems.splice(index, 1);
    setChecklistItems(newItems);
  };

  // Handle save button click
  const handleSave = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    try {
      // Exit edit mode first
      setIsEditing(false);

      // Save the item with the content and formatting
      onUpdateText(textItem.id, {
        content: editValue,
        isNew: false,
        isRichText: false,
        // @ts-ignore
        textStyle,
        // @ts-ignore
        textAlign,
        isBold,
        isItalic,
        isUnderline,
        color: textColor,
        backgroundColor: bgColor,
        isChecklist,
        checklistItems
      });

      toast.success('Text saved', { duration: 2000 });
    } catch (error) {
      console.error('Error saving text:', error);
      toast.error('Failed to save text');
      setIsEditing(false);
    }
  };

  // Cancel editing
  const handleCancel = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }

    setEditValue(textItem.content || '');
    setIsEditing(false);
  };

  // Handle delete button click
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (showDeleteConfirm) {
      onRemoveText(textItem.id);
      toast.success('Text card removed', { duration: 2000 });
    } else {
      setShowDeleteConfirm(true);
      setTimeout(() => setShowDeleteConfirm(false), 3000);
    }
  };

  // Handle mouse enter/leave
  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => {
    setIsHovered(false);
    setShowDeleteConfirm(false);
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing) {
      // Ctrl+Enter or Cmd+Enter to save
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleSave();
      }
      // Escape to cancel
      else if (e.key === 'Escape') {
        e.preventDefault();
        handleCancel();
      }
    }
  };

  // Determine text alignment style
  const textAlignStyle = textItem.textAlign || 'left';
  const isTitleStyle = textItem.textStyle === 'title';

  return (
    <Card
      className={cn(
        "w-full h-full transition-all duration-200 dashboard-card-group",
        isEditMode ? "hover:border-primary border" : "border-0 shadow-none no-border",
        isEditing ? "ring-2 ring-primary border" : "",
        isTitleStyle ? "title-text-card" : "content-text-card"
      )}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      data-item-id={textItem.id}
      data-item-type="text"
      data-is-editing={isEditing ? 'true' : 'false'}
      data-edit-mode={isEditMode ? 'true' : 'false'}
      onKeyDown={handleKeyDown}
    >
      {/* Hover-based Remove Button */}
      {isEditMode && !isEditing && (
        <Button
          variant="ghost"
          size="sm"
          className="card-remove-button h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-all non-draggable"
          onClick={handleDelete}
          onMouseDown={(e) => e.stopPropagation()}
          style={{ pointerEvents: 'auto' }}
          title="Delete card"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      )}

      {/* Edit Button - Show on double click or when not editing */}
      {isEditMode && !isEditing && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 left-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-blue-100 hover:text-blue-600 transition-all non-draggable"
          onClick={() => setIsEditing(true)}
          onMouseDown={(e) => e.stopPropagation()}
          style={{ pointerEvents: 'auto' }}
          title="Edit text"
        >
          <Edit className="h-3 w-3" />
        </Button>
      )}

      {/* Drag Handle */}
      {isEditMode && !isEditing && (
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 cursor-grab draggable-handle"
            style={{ pointerEvents: 'auto' }}
          >
            <Grip className="h-4 w-4 text-muted-foreground" />
          </div>
        </>
      )}

      <CardContent
        className={cn(
          "p-3 h-full",
          isTitleStyle ? "flex items-center justify-center" : "",
          bgColor ? "" : "bg-card dark:bg-card"
        )}
        style={{
          backgroundColor: bgColor || (resolvedTheme === 'dark' ? 'hsl(var(--card))' : 'hsl(var(--card))')
        }}
      >
        {isEditing ? (
          // Edit mode with formatting toolbar and textarea
          <div className="w-full h-full flex flex-col non-draggable">
            {/* Toolbar */}
            <div className="flex items-center justify-between p-1 border-b bg-muted/20">
              {/* Formatting Controls */}
              <div className="flex items-center gap-1 overflow-x-auto">
                {/* Text Style Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 px-2 text-xs gap-1">
                      <Type className="h-3.5 w-3.5" />
                      <span>
                        {textStyle === 'normal' ? 'Normal' :
                         textStyle === 'h1' ? 'Heading 1' :
                         textStyle === 'h2' ? 'Heading 2' :
                         textStyle === 'h3' ? 'Heading 3' :
                         textStyle === 'h4' ? 'Heading 4' :
                         textStyle === 'title' ? 'Title' : 'Normal'}
                      </span>
                      <ChevronDown className="h-3 w-3 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-48">
                    <DropdownMenuItem onClick={() => setHeadingStyle('normal')}>
                      <Type className="h-4 w-4 mr-2" />
                      Normal
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setHeadingStyle('h1')}>
                      <Heading1 className="h-4 w-4 mr-2" />
                      Heading 1
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setHeadingStyle('h2')}>
                      <Heading2 className="h-4 w-4 mr-2" />
                      Heading 2
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setHeadingStyle('h3')}>
                      <Heading3 className="h-4 w-4 mr-2" />
                      Heading 3
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setHeadingStyle('title')}>
                      <Type className="h-4 w-4 mr-2" />
                      Title
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Text Formatting Buttons */}
                <Button
                  variant={isBold ? "default" : "ghost"}
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={toggleBold}
                >
                  <Bold className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant={isItalic ? "default" : "ghost"}
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={toggleItalic}
                >
                  <Italic className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant={isUnderline ? "default" : "ghost"}
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={toggleUnderline}
                >
                  <Underline className="h-3.5 w-3.5" />
                </Button>

                {/* Text Alignment */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      {textAlign === 'left' ? <AlignLeft className="h-3.5 w-3.5" /> :
                       textAlign === 'center' ? <AlignCenter className="h-3.5 w-3.5" /> :
                       textAlign === 'right' ? <AlignRight className="h-3.5 w-3.5" /> :
                       <AlignJustify className="h-3.5 w-3.5" />}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-32">
                    <DropdownMenuItem onClick={() => setAlignment('left')}>
                      <AlignLeft className="h-4 w-4 mr-2" />
                      Left
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setAlignment('center')}>
                      <AlignCenter className="h-4 w-4 mr-2" />
                      Center
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setAlignment('right')}>
                      <AlignRight className="h-4 w-4 mr-2" />
                      Right
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setAlignment('justify')}>
                      <AlignJustify className="h-4 w-4 mr-2" />
                      Justify
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Color Picker */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <Palette className="h-3.5 w-3.5" style={{ color: textColor || 'currentColor' }} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-2">
                    <div className="grid grid-cols-6 gap-1">
                      {['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF',
                        '#00FFFF', '#FFA500', '#800080', '#008000', '#800000', '#008080',
                        '#000080', '#FFC0CB', '#A52A2A', '#808080', '#FFFFFF', ''].map((color, i) => (
                        <Button
                          key={i}
                          variant="outline"
                          className="h-6 w-6 p-0 border"
                          style={{
                            backgroundColor: color || 'transparent',
                            borderColor: color === textColor ? 'hsl(var(--primary))' : 'hsl(var(--border))'
                          }}
                          onClick={() => handleColorChange(color)}
                        >
                          {color === '' && <Palette className="h-3 w-3 text-muted-foreground" />}
                        </Button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Background Color Picker */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <div className="h-3.5 w-3.5 border rounded" style={{ backgroundColor: bgColor || 'transparent' }} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-2">
                    <div className="grid grid-cols-6 gap-1">
                      {['#FFFFFF', '#F8F9FA', '#F1F3F5', '#E9ECEF', '#DEE2E6', '#CED4DA',
                        '#ADB5BD', '#868E96', '#495057', '#343A40', '#212529', '#000000',
                        '#FFE3E3', '#FFF0F5', '#F3F0FF', '#E5DBFF', '#D0EBFF', '#C5F6FA',
                        '#E6FCF5', '#D3F9D8', '#FFF9DB', '#FFE8CC', ''].map((color, i) => (
                        <Button
                          key={i}
                          variant="outline"
                          className="h-6 w-6 p-0 border"
                          style={{
                            backgroundColor: color || 'transparent',
                            borderColor: color === bgColor ? 'hsl(var(--primary))' : 'hsl(var(--border))'
                          }}
                          onClick={() => handleBgColorChange(color)}
                        >
                          {color === '' && <Palette className="h-3 w-3 text-muted-foreground" />}
                        </Button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Checklist Toggle */}
                <Button
                  variant={isChecklist ? "default" : "ghost"}
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={toggleChecklist}
                >
                  <List className="h-3.5 w-3.5" />
                </Button>
              </div>

              {/* Save/Cancel Buttons */}
              <div className="ml-auto flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs non-draggable"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="h-6 text-xs non-draggable"
                  onClick={handleSave}
                >
                  Done
                </Button>
              </div>
            </div>

            {/* Text Editor */}
            <div className="w-full h-full p-1 non-draggable overflow-y-auto" style={{ minHeight: '100px' }}>
              {isChecklist ? (
                // Checklist Editor
                <div className="space-y-2 pt-2">
                  {checklistItems.map((item, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-5 w-5 p-0 mt-1"
                        onClick={() => toggleChecklistItem(index)}
                      >
                        {item.checked ? <Check className="h-3 w-3" /> : <Square className="h-3 w-3" />}
                      </Button>
                      <Textarea
                        value={item.text}
                        onChange={(e) => updateChecklistItemText(index, e.target.value)}
                        className="flex-1 min-h-[30px] resize-none border-none focus-visible:ring-0 p-0"
                        placeholder="List item..."
                        style={{
                          textDecoration: item.checked ? 'line-through' : 'none',
                          color: item.checked ? 'hsl(var(--muted-foreground))' : textColor || 'inherit'
                        }}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-5 w-5 p-0 mt-1"
                        onClick={() => removeChecklistItem(index)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                    onClick={addChecklistItem}
                  >
                    + Add item
                  </Button>
                </div>
              ) : (
                // Regular Text Editor
                <Textarea
                  ref={textareaRef}
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  onSelect={handleTextSelection}
                  onMouseUp={handleTextSelection}
                  onKeyUp={handleTextSelection}
                  className="w-full h-full min-h-[100px] resize-none border-none focus-visible:ring-0 p-0"
                  placeholder="Enter your text here..."
                  style={{
                    textAlign: textAlign as any,
                    fontSize:
                      textStyle === 'h1' ? '1.8rem' :
                      textStyle === 'h2' ? '1.5rem' :
                      textStyle === 'h3' ? '1.3rem' :
                      textStyle === 'h4' ? '1.1rem' :
                      textStyle === 'title' ? '1.5rem' : '1rem',
                    fontWeight:
                      (textStyle === 'h1' || textStyle === 'h2' || textStyle === 'h3' || textStyle === 'title' || isBold) ? 'bold' : 'normal',
                    fontStyle: isItalic ? 'italic' : 'normal',
                    textDecoration: isUnderline ? 'underline' : 'none',
                    color: textColor || 'inherit',
                    backgroundColor: 'transparent'
                  }}
                />
              )}
            </div>

            <div className="absolute bottom-3 right-3 bg-primary/10 text-primary text-xs px-2 py-1 rounded-sm">
              Ctrl+Enter to save, Esc to cancel
            </div>
          </div>
        ) : (
          // View-only mode
          <div
            className={cn(
              "w-full h-full simple-text-content",
              textStyle === 'h1' ? "text-3xl font-bold" :
              textStyle === 'h2' ? "text-2xl font-bold" :
              textStyle === 'h3' ? "text-xl font-bold" :
              textStyle === 'h4' ? "text-lg font-bold" :
              textStyle === 'title' ? "text-2xl font-bold" : ""
            )}
            style={{
              minHeight: '100px',
              backgroundColor: 'transparent',
              textAlign: textAlign as any,
              fontStyle: isItalic ? 'italic' : 'normal',
              textDecoration: isUnderline ? 'underline' : 'none',
              fontWeight: isBold ? 'bold' : 'normal',
              color: textColor || 'inherit',
              whiteSpace: 'pre-wrap',
              overflowWrap: 'break-word'
            }}
          >
            {isChecklist && checklistItems.length > 0 ? (
              // Render checklist items
              <div className="space-y-2">
                {checklistItems.map((item, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="h-4 w-4 mt-1 border rounded flex items-center justify-center">
                      {item.checked && <Check className="h-3 w-3" />}
                    </div>
                    <div
                      style={{
                        textDecoration: item.checked ? 'line-through' : 'none',
                        color: item.checked ? 'hsl(var(--muted-foreground))' : 'inherit'
                      }}
                    >
                      {item.text}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Render formatted text with HTML support
              <div
                dangerouslySetInnerHTML={{
                  __html: editValue
                    // Convert markdown-style formatting to HTML
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
                    .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
                    .replace(/__(.*?)__/g, '<u>$1</u>') // Underline
                    .replace(/^# (.*?)$/gm, '<h1>$1</h1>') // H1
                    .replace(/^## (.*?)$/gm, '<h2>$1</h2>') // H2
                    .replace(/^### (.*?)$/gm, '<h3>$1</h3>') // H3
                    .replace(/^#### (.*?)$/gm, '<h4>$1</h4>') // H4
                    .replace(/\n/g, '<br/>') // Line breaks
                    || textItem.placeholder
                    || 'Click to edit text'
                }}
              />
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
