import React from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Quote, Database } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ReferenceDetailsDialogProps {
  selectedReference: { dataset: string; reference: string; type: string } | null;
  setSelectedReference: (ref: { dataset: string; reference: string; type: string } | null) => void;
}

const ReferenceDetailsDialog: React.FC<ReferenceDetailsDialogProps> = ({ selectedReference, setSelectedReference }) => {
  if (!selectedReference) return null;
  return (
    <Dialog open={true} onOpenChange={() => setSelectedReference(null)}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Quote className="h-4 w-4" />
            Reference Details
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium">Dataset:</label>
            <div className="flex items-center gap-2 mt-1">
              <Database className="h-4 w-4" />
              <span className="text-sm">{selectedReference.dataset}</span>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium">Type:</label>
            <Badge variant="secondary" className="ml-2">
              {selectedReference.type}
            </Badge>
          </div>
          <div>
            <label className="text-sm font-medium">Reference:</label>
            <div className="mt-1 p-3 bg-muted rounded-md text-sm">
              {selectedReference.reference}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReferenceDetailsDialog; 