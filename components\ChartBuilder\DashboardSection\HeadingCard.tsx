'use client'

/**
 * HeadingCard Component
 *
 * This component renders a heading card in the dashboard that allows users to add titles and section headers.
 * It supports different heading levels (h1-h6) and provides a rich editing experience.
 *
 * Features:
 * - Multiple heading levels (h1, h2, h3, etc.)
 * - Double-click to edit heading text
 * - Drag-and-drop positioning in edit mode
 * - Resizable headings with custom resize handles
 * - Confirmation dialog when deleting headings
 */

import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { HeadingItem } from './types';
import { Grip, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

interface HeadingCardProps {
  heading: HeadingItem;
  isEditMode: boolean;
  onUpdateHeading: (headingId: string, updates: Partial<HeadingItem>) => void;
  onRemoveHeading: (headingId: string) => void;
}

export function HeadingCard({
  heading,
  isEditMode,
  onUpdateHeading,
  onRemoveHeading
}: HeadingCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [content, setContent] = useState(heading.content);
  const cardRef = useRef<HTMLDivElement>(null);

  // Update content state when heading content changes
  // This ensures the component stays in sync with parent updates
  useEffect(() => {
    setContent(heading.content);
  }, [heading.content]);

  // Resize handlers for each corner and edge
  const handleResize = (direction: string, movementX: number, movementY: number) => {
    if (!cardRef.current) return;

    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    let newWidth = rect.width;
    let newHeight = rect.height;

    // Update dimensions based on resize direction
    switch (direction) {
      case 'e':
        newWidth = rect.width + movementX;
        break;
      case 's':
        newHeight = rect.height + movementY;
        break;
      case 'se':
        newWidth = rect.width + movementX;
        newHeight = rect.height + movementY;
        break;
    }

    // Apply minimum dimensions
    newWidth = Math.max(200, newWidth);
    newHeight = Math.max(80, newHeight);

    onUpdateHeading(heading.id, {
      width: newWidth,
      height: newHeight
    });
  };

  // Custom resize handle component
  const ResizeHandle = ({ direction }: { direction: string }) => {
    const [isResizing, setIsResizing] = useState(false);

    const handleMouseDown = (e: React.MouseEvent) => {
      if (!isEditMode) return;
      e.preventDefault();
      setIsResizing(true);

      const startX = e.clientX;
      const startY = e.clientY;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        const movementX = moveEvent.clientX - startX;
        const movementY = moveEvent.clientY - startY;
        handleResize(direction, movementX, movementY);
      };

      const handleMouseUp = () => {
        setIsResizing(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    return (
      <div
        onMouseDown={handleMouseDown}
        className={`absolute bg-primary/20 hover:bg-primary/40 transition-colors
          ${isEditMode ? 'visible' : 'invisible'}
          ${isResizing ? 'bg-primary/60' : ''}
          ${direction === 'e' ? 'right-0 top-0 w-1 h-full cursor-ew-resize' : ''}
          ${direction === 's' ? 'bottom-0 left-0 w-full h-1 cursor-ns-resize' : ''}
          ${direction === 'se' ? 'bottom-0 right-0 w-3 h-3 cursor-se-resize rounded-br' : ''}
        `}
      />
    );
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setContent(e.target.value);
  };

  const handleContentBlur = () => {
    // Only exit edit mode if content is not empty
    if (content.trim() !== '') {
      setIsEditing(false);
      if (content !== heading.content) {
        onUpdateHeading(heading.id, { content });
      }
    } else {
      // If content is empty, keep focus and show a toast message
      toast.error('Please enter some text for the heading');
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Double click detected on heading:', heading.id);

    if (isEditMode) {
      console.log('Entering edit mode due to double click');
      setIsEditing(true);
    }
  };

  // Additional handler for clicking the edit button
  const handleEditClick = () => {
    console.log('Edit button clicked for heading:', heading.id);
    setIsEditing(true);
  };

  // Set editing mode when a new heading is added
  // This is useful for newly dragged headings
  useEffect(() => {
    // Check if this is a newly created heading (either by isNew flag or empty content)
    const isNewHeading = heading.isNew || heading.content === '';

    // Auto-enter edit mode for new headings if in edit mode
    if (isNewHeading && isEditMode && !isEditing) {
      console.log('Auto-entering edit mode for new heading:', heading.id);
      // Use a small timeout to ensure the component is fully mounted
      setTimeout(() => {
        setIsEditing(true);
        console.log('Edit mode set to true for heading:', heading.id);
      }, 100);

      // Clear the isNew flag after entering edit mode
      if (heading.isNew) {
        onUpdateHeading(heading.id, { isNew: false });
      }
    }
  }, [heading.id, heading.isNew, heading.content, isEditMode, isEditing]);

  // Log when the component renders
  useEffect(() => {
    console.log('HeadingCard rendered for heading:', heading.id, 'isEditing:', isEditing);
    return () => {
      console.log('HeadingCard unmounted for heading:', heading.id);
    };
  }, [heading.id]);

  // Render the appropriate heading element based on the heading level
  const renderHeading = () => {
    const headingStyle = {
      textAlign: heading.textAlign || 'center', // Default to center alignment
      color: heading.textColor || 'inherit',
      backgroundColor: heading.backgroundColor || 'transparent',
    } as React.CSSProperties;

    if (isEditing) {
      return (
        <div className="relative w-full">
          <Input
            value={content}
            onChange={handleContentChange}
            onBlur={handleContentBlur}
            onKeyDown={(e) => {
              // Submit on Enter key
              if (e.key === 'Enter') {
                handleContentBlur();
              }
            }}
            autoFocus
            className="w-full h-full border-2 border-primary text-inherit font-inherit px-3 py-2 bg-white dark:bg-gray-800 shadow-inner focus:ring-2 focus:ring-primary"
            style={{
              ...headingStyle,
              fontSize: heading.headingLevel === 'h1' ? '1.5rem' : '1.25rem',
              fontWeight: 'bold'
            }}
            placeholder={heading.placeholder || `Enter ${heading.headingLevel.toUpperCase()} text...`}
          />
          <div className="absolute -top-2 right-2 bg-primary text-white text-xs px-2 py-0.5 rounded-full animate-pulse">
            Editing - Press Enter when done
          </div>
        </div>
      );
    }

    // Add a hint and edit button for editing in edit mode
    const editHint = isEditMode ? (
      <>
        <div className="absolute -top-2 right-2 bg-muted text-muted-foreground text-xs px-2 py-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
          Double-click to edit
        </div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="outline"
            size="sm"
            className="bg-primary/90 text-primary-foreground hover:bg-primary border-none shadow-md"
            onClick={handleEditClick}
          >
            Edit Text
          </Button>
        </div>
      </>
    ) : null;

    // Wrapper with relative positioning for the hint
    const wrapHeading = (element: React.ReactNode) => (
      <div className="relative group">
        {element}
        {editHint}
      </div>
    );

    switch (heading.headingLevel) {
      case 'h1':
        return wrapHeading(<h1 className="text-4xl font-bold text-primary" style={headingStyle}>{heading.content}</h1>);
      case 'h2':
        return wrapHeading(<h2 className="text-3xl font-bold text-secondary" style={headingStyle}>{heading.content}</h2>);
      case 'h3':
        return wrapHeading(<h3 className="text-2xl font-semibold" style={headingStyle}>{heading.content}</h3>);
      case 'h4':
        return wrapHeading(<h4 className="text-xl font-semibold" style={headingStyle}>{heading.content}</h4>);
      case 'h5':
        return wrapHeading(<h5 className="text-lg font-medium" style={headingStyle}>{heading.content}</h5>);
      case 'h6':
        return wrapHeading(<h6 className="text-base font-medium" style={headingStyle}>{heading.content}</h6>);
      default:
        return wrapHeading(<p className="text-base" style={headingStyle}>{heading.content}</p>);
    }
  };

  return (
    <motion.div
      ref={cardRef}
      drag={isEditMode}
      dragMomentum={false}
      dragElastic={0}
      onDragStart={() => setIsDragging(true)}
      onDragEnd={(_, info) => {
        setIsDragging(false);
        onUpdateHeading(heading.id, {
          gridColumn: Math.round(info.point.x / 100),
          gridRow: Math.round(info.point.y / 100)
        });
      }}
      style={{
        width: heading.width || 350,
        height: heading.height || 80,
        zIndex: isDragging ? 9999 : isEditing ? 2000 : 1000
      }}
      className={`
        relative rounded-lg dashboard-card-group
        ${isEditMode ? 'border-2 bg-card shadow-sm hover:shadow-md' : 'border border-muted/30 bg-card/80 shadow-sm'}
        ${isDragging ? 'z-50 cursor-grabbing' : isEditMode ? 'cursor-grab' : ''}
        ${isEditMode ? 'hover:border-primary' : 'hover:shadow-md'}
        ${isEditing ? 'ring-2 ring-primary ring-offset-2 shadow-lg' : ''}
        transition-all duration-200
      `}
      onDoubleClick={handleDoubleClick}
    >
      {/* Card Content */}
      <div className="flex flex-col h-full">
        {/* Card Header - Always shown, but with different styles in edit mode */}
        <div className={`flex items-center justify-between p-1 border-b ${isEditMode ? 'bg-gradient-to-r from-background to-muted/10' : 'bg-muted/5'}`}>
          <div className="flex items-center gap-1">
            {isEditMode && <Grip className="h-3 w-3 text-muted-foreground" />}
            <span className={`text-[10px] font-medium ${isEditMode ? 'text-muted-foreground' : 'text-muted-foreground/70'}`}>
              {heading.headingLevel.toUpperCase()} TEXT
            </span>
          </div>
          <div className="flex items-center gap-1">
            {!isEditMode && (
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleEditClick}
              >
                <span className="text-[8px]">Edit</span>
              </Button>
            )}
          </div>
        </div>

        {/* Hover-based Remove Button */}
        {isEditMode && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="card-remove-button h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-all"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Heading</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this heading? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onRemoveHeading(heading.id)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        <div className="flex items-center gap-1">
          </div>
        </div>

        {/* Heading Content - More compact */}
        <div className={`flex-1 ${isEditMode ? 'p-1' : 'p-0'} flex items-center justify-center overflow-hidden`}>
          <div className="w-full">
            {renderHeading()}
          </div>
        </div>

        {/* Resize Handles */}
        {isEditMode && (
          <>
            <ResizeHandle direction="e" />
            <ResizeHandle direction="s" />
            <ResizeHandle direction="se" />
          </>
        )}
      </div>
    </motion.div>
  );
}
